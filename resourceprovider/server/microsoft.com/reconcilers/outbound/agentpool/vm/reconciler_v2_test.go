package vm

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/compute/armcompute/v5"
	. "github.com/onsi/ginkgo"
	ginkgotable "github.com/onsi/ginkgo/extensions/table"
	. "github.com/onsi/gomega"
	"go.goms.io/aks/rp/devinfraservices/toggles/rollout"
	"go.goms.io/aks/rp/devinfraservices/toggles/rollout/fake"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/containerservice/flags"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool/vm/nic/mock_nic"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/azureresources/mock_azureresources"
	azureresources_test "go.goms.io/aks/rp/resourceprovider/sharedlib/azureresources/test"
	cgerror "go.goms.io/aks/rp/toolkit/categorizederror"
	"go.goms.io/aks/rp/toolkit/log"
	"go.goms.io/aks/rp/toolkit/testlib"
	gomock "go.uber.org/mock/gomock"
)

var _ = Describe("ReconcilerV2", func() {
	var ctx context.Context
	var mockCtrl *gomock.Controller
	var vmClient *mock_azureresources.MockVirtualMachineInterface
	var nicReconciler *mock_nic.MockNicInterfaceReconciler
	var reconciler *VMAgentpoolBackendpoolReconcilerV2

	var (
		backendpoolIDs     map[string]struct{}
		backendpoolIDsIPV6 map[string]struct{}
	)

	BeforeEach(func() {
		ctx = log.WithLogger(context.Background(), log.InitializeTestLogger())
		mockCtrl = gomock.NewController(GinkgoT())
		vmClient = mock_azureresources.NewMockVirtualMachineInterface(mockCtrl)
		nicReconciler = mock_nic.NewMockNicInterfaceReconciler(mockCtrl)
		toggle := fake.New()
		toggle.Override("enable-list-nics-using-arg", "true")
		useTrack2, _ := strconv.ParseBool(os.Getenv("USE_TRACK2"))
		if useTrack2 {
			toggle.Override("enable-sdk-track2-slb-outbound-reconciler", "true")
		}
		flag := flags.NewFlags(toggle, &rollout.Entity{})
		reconciler = &VMAgentpoolBackendpoolReconcilerV2{
			flags:         flag,
			vmClient:      vmClient,
			nicReconciler: nicReconciler,
		}
	})
	AfterEach(func() {
		mockCtrl.Finish()
	})

	Context("listAllOfVMInterfaces", func() {
		var (
			VMWithSingleNic = &armcompute.VirtualMachine{
				ID:   to.Ptr("VMWithSingleNic"),
				Name: to.Ptr("VMWithSingleNic"),
				Tags: map[string]*string{
					"poolName": to.Ptr("poolName"),
				},
				Properties: &armcompute.VirtualMachineProperties{
					NetworkProfile: &armcompute.NetworkProfile{
						NetworkInterfaces: []*armcompute.NetworkInterfaceReference{
							{
								ID: to.Ptr("VMWithSingleNic1"),
							},
						},
					},
					InstanceView: &armcompute.VirtualMachineInstanceView{
						Statuses: []*armcompute.InstanceViewStatus{
							{
								Code: to.Ptr("PowerState/Running"),
							},
						},
					},
				},
			}
			VMWithoutInstanceView = &armcompute.VirtualMachine{
				ID:   to.Ptr("VMWithoutInstanceView"),
				Name: to.Ptr("VMWithoutInstanceView"),
				Tags: map[string]*string{
					"poolName": to.Ptr("poolName"),
				},
				Properties: &armcompute.VirtualMachineProperties{
					NetworkProfile: &armcompute.NetworkProfile{
						NetworkInterfaces: []*armcompute.NetworkInterfaceReference{
							{
								ID: to.Ptr("VMWithoutInstanceView"),
							},
						},
					},
				},
			}
			VMWithMultipleNics = &armcompute.VirtualMachine{
				ID:   to.Ptr("VMWithMultipleNics"),
				Name: to.Ptr("VMWithMultipleNics"),
				Tags: map[string]*string{
					"poolName": to.Ptr("poolName"),
				},
				Properties: &armcompute.VirtualMachineProperties{
					NetworkProfile: &armcompute.NetworkProfile{
						NetworkInterfaces: []*armcompute.NetworkInterfaceReference{
							{
								ID: to.Ptr("VMWithMultipleNics1"),
								Properties: &armcompute.NetworkInterfaceReferenceProperties{
									Primary: to.Ptr(true),
								},
							},
							{
								ID: to.Ptr("VMWithMultipleNics2"),
							},
						},
					},
					InstanceView: &armcompute.VirtualMachineInstanceView{
						Statuses: []*armcompute.InstanceViewStatus{
							{
								Code: to.Ptr("PowerState/Running"),
							},
						},
					},
				},
			}
			UnmanagedVM = &armcompute.VirtualMachine{
				ID:   to.Ptr("UnmanagedVM"),
				Name: to.Ptr("UnmanagedVM"),
				Properties: &armcompute.VirtualMachineProperties{
					NetworkProfile: &armcompute.NetworkProfile{
						NetworkInterfaces: []*armcompute.NetworkInterfaceReference{
							{
								ID: to.Ptr("UnmanagedVM"),
								Properties: &armcompute.NetworkInterfaceReferenceProperties{
									Primary: to.Ptr(true),
								},
							},
							{
								ID: to.Ptr("UnmanagedVM"),
							},
						},
					},
					InstanceView: &armcompute.VirtualMachineInstanceView{
						Statuses: []*armcompute.InstanceViewStatus{
							{
								Code: to.Ptr("PowerState/Running"),
							},
						},
					},
				},
			}
			StoppedVM = &armcompute.VirtualMachine{
				ID:   to.Ptr("StoppedVM"),
				Name: to.Ptr("StoppedVM"),
				Tags: map[string]*string{
					"poolName": to.Ptr("poolName"),
				},
				Properties: &armcompute.VirtualMachineProperties{
					NetworkProfile: &armcompute.NetworkProfile{
						NetworkInterfaces: []*armcompute.NetworkInterfaceReference{
							{
								ID: to.Ptr("StoppedVM"),
								Properties: &armcompute.NetworkInterfaceReferenceProperties{
									Primary: to.Ptr(true),
								},
							},
							{
								ID: to.Ptr("StoppedVM"),
							},
						},
					},
					InstanceView: &armcompute.VirtualMachineInstanceView{
						Statuses: []*armcompute.InstanceViewStatus{
							{
								Code: to.Ptr("PowerState/Stopped"),
							},
						},
					},
				},
			}
			invalidVMWithMultipleNics = &armcompute.VirtualMachine{
				ID:   to.Ptr("invalidVMWithMultipleNics"),
				Name: to.Ptr("invalidVMWithMultipleNics"),
				Tags: map[string]*string{
					"poolName": to.Ptr("poolName"),
				},
				Properties: &armcompute.VirtualMachineProperties{
					NetworkProfile: &armcompute.NetworkProfile{
						NetworkInterfaces: []*armcompute.NetworkInterfaceReference{
							{
								ID: to.Ptr("VMWithMultipleNics1"),
							},
							{
								ID: to.Ptr("VMWithMultipleNics2"),
							},
						},
					},
					InstanceView: &armcompute.VirtualMachineInstanceView{
						Statuses: []*armcompute.InstanceViewStatus{
							{
								Code: to.Ptr("PowerState/Running"),
							},
						},
					},
				},
			}
			vmInterminatingstate = &armcompute.VirtualMachine{
				ID:   to.Ptr("invalidVMWithMultipleNics"),
				Name: to.Ptr("invalidVMWithMultipleNics"),
				Tags: map[string]*string{
					"poolName": to.Ptr("poolName"),
				},
				Properties: &armcompute.VirtualMachineProperties{
					NetworkProfile: &armcompute.NetworkProfile{
						NetworkInterfaces: []*armcompute.NetworkInterfaceReference{
							{
								ID: to.Ptr("VMWithMultipleNics1"),
							},
							{
								ID: to.Ptr("VMWithMultipleNics2"),
							},
						},
					},
					ProvisioningState: to.Ptr("Deallocating"),
					InstanceView: &armcompute.VirtualMachineInstanceView{
						Statuses: []*armcompute.InstanceViewStatus{
							{
								Code: to.Ptr("PowerState/Running"),
							},
						},
					},
				},
			}
		)
		BeforeEach(func() {
			VMWithoutInstanceView.Properties.InstanceView = nil
		})

		ginkgotable.DescribeTable("listAllOfVMInterfaces validation table", func(vmLists []*armcompute.VirtualMachine, generatedListError *cgerror.CategorizedError, generatedGetError *cgerror.CategorizedError, expectedIDMap map[string]struct{}, expectedErr error) {
			mockCtrl := gomock.NewController(GinkgoT())
			defer mockCtrl.Finish()
			vmClient = mock_azureresources.NewMockVirtualMachineInterface(mockCtrl)
			nicReconciler = mock_nic.NewMockNicInterfaceReconciler(mockCtrl)
			toggle := fake.New()
			toggle.Override("enable-list-nics-using-arg", "true")
			useTrack2, _ := strconv.ParseBool(os.Getenv("USE_TRACK2"))
			if useTrack2 {
				toggle.Override("enable-sdk-track2-slb-outbound-reconciler", "true")
			}
			flag := flags.NewFlags(toggle, &rollout.Entity{})
			reconciler = &VMAgentpoolBackendpoolReconcilerV2{
				flags:         flag,
				vmClient:      vmClient,
				nicReconciler: nicReconciler,
			}

			vmClient.EXPECT().ListAll(testlib.IsContext(), gomock.Any(), azureresources_test.ListVMWithInstanceViewAndARG()).Return(vmLists, generatedListError)
			vmClient.EXPECT().InstanceView(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
				armcompute.VirtualMachinesClientInstanceViewResponse{
					VirtualMachineInstanceView: armcompute.VirtualMachineInstanceView{
						Statuses: []*armcompute.InstanceViewStatus{
							{
								Code: to.Ptr("PowerState/Running"),
							},
						},
					},
				}, generatedGetError).AnyTimes()
			nicList, err := reconciler.listAllOfVMInterfaces(ctx, computeSubscriptionID, resourceGroupName)
			if expectedErr != nil {
				Expect(err).To(BeEquivalentTo(expectedErr))
			} else {
				Expect(err).To(BeNil())
			}
			Expect(nicList).To(BeEquivalentTo(expectedIDMap))
		},
			ginkgotable.Entry("vmlist is nil", nil, nil, nil, nil, nil),
			ginkgotable.Entry("vmclient returned error", nil, cgerror.ToCategorizedError(fmt.Errorf("failed to return list resuilt")), nil, nil, cgerror.ToCategorizedError(fmt.Errorf("failed to return list resuilt"))),
			ginkgotable.Entry("vmclient returned 1 valid vm", []*armcompute.VirtualMachine{
				VMWithSingleNic,
			}, nil, nil, map[string]struct{}{
				strings.ToLower("VMWithSingleNic1"): {},
			}, nil),
			ginkgotable.Entry("vmclient returned 1 valid vm", []*armcompute.VirtualMachine{
				VMWithoutInstanceView,
			}, nil, nil, map[string]struct{}{
				strings.ToLower("VMWithoutInstanceView"): {},
			}, nil),
			ginkgotable.Entry("vmclient returned 1 valid vm", []*armcompute.VirtualMachine{
				VMWithoutInstanceView,
			}, nil, cgerror.CreateInternalError(), nil, cgerror.CreateInternalError()),
			ginkgotable.Entry("vmclient returned 1 valid vm with multiple nics ", []*armcompute.VirtualMachine{
				VMWithMultipleNics,
			}, nil, nil, map[string]struct{}{
				strings.ToLower("VMWithMultipleNics1"): {},
			}, nil),
			ginkgotable.Entry("vmclient returned 1 invalid vm with multiple nics ", []*armcompute.VirtualMachine{
				invalidVMWithMultipleNics,
			}, nil, nil, nil, nil),
			ginkgotable.Entry("vmclient returned multiple vms including 1 invalid vm", []*armcompute.VirtualMachine{
				invalidVMWithMultipleNics,
				VMWithMultipleNics,
				VMWithSingleNic,
			}, nil, nil, map[string]struct{}{
				strings.ToLower("VMWithMultipleNics1"): {},
				strings.ToLower("VMWithSingleNic1"):    {},
			}, nil),
			ginkgotable.Entry("vmclient returned 1 unmanaged vm", []*armcompute.VirtualMachine{
				UnmanagedVM,
			}, nil, nil, nil, nil),
			ginkgotable.Entry("vmclient returned 1 vm in terminating state", []*armcompute.VirtualMachine{
				vmInterminatingstate,
			}, nil, nil, nil, nil),
			ginkgotable.Entry("vmclient returned 1 vm in stopped state", []*armcompute.VirtualMachine{
				StoppedVM,
			}, nil, nil, nil, nil),
			ginkgotable.Entry("vmclient returned 1 vm without instance view", []*armcompute.VirtualMachine{
				{
					ID:   to.Ptr("VMWithSingleNic"),
					Name: to.Ptr("VMWithSingleNic"),
					Tags: map[string]*string{
						"poolName": to.Ptr("poolName"),
					},
					Properties: &armcompute.VirtualMachineProperties{
						NetworkProfile: &armcompute.NetworkProfile{
							NetworkInterfaces: []*armcompute.NetworkInterfaceReference{
								{
									ID: to.Ptr("VMWithSingleNic1"),
								},
							},
						},
					},
				},
			}, nil, nil, map[string]struct{}{
				strings.ToLower("VMWithSingleNic1"): {},
			}, nil),
			ginkgotable.Entry("vmclient returned invalid vm", []*armcompute.VirtualMachine{
				{
					ID:   to.Ptr("nilVirtualMachineProperties"),
					Name: to.Ptr("invalidVMWithMultipleNics"),
				},
				{
					ID:         to.Ptr("nilNetworkProfile"),
					Name:       to.Ptr("invalidVMWithMultipleNics"),
					Properties: &armcompute.VirtualMachineProperties{},
				},
				{
					ID:   to.Ptr("nilNetworkInterfaces"),
					Name: to.Ptr("invalidVMWithMultipleNics"),
					Properties: &armcompute.VirtualMachineProperties{
						NetworkProfile: &armcompute.NetworkProfile{},
					},
				},
				{
					ID:   to.Ptr("nilNetworkInterfacesID"),
					Name: to.Ptr("invalidVMWithMultipleNics"),
					Properties: &armcompute.VirtualMachineProperties{
						NetworkProfile: &armcompute.NetworkProfile{
							NetworkInterfaces: []*armcompute.NetworkInterfaceReference{
								{},
							},
						},
					},
				},
			}, nil, nil, nil, nil),
		)
	})

	Context("DecoupleLBBackendPool", func() {
		var nicMap map[string]struct{}
		BeforeEach(func() {
			vmClient.EXPECT().ListAll(testlib.IsContext(), gomock.Any(), azureresources_test.ListVMWithInstanceViewAndARG()).Return([]*armcompute.VirtualMachine{
				{
					ID:   to.Ptr("VMWithSingleNic"),
					Name: to.Ptr("VMWithSingleNic"),
					Tags: map[string]*string{
						"poolName": to.Ptr("poolName"),
					},
					Properties: &armcompute.VirtualMachineProperties{
						NetworkProfile: &armcompute.NetworkProfile{
							NetworkInterfaces: []*armcompute.NetworkInterfaceReference{
								{
									ID: to.Ptr("VMWithSingleNic1"),
								},
							},
						},
						InstanceView: &armcompute.VirtualMachineInstanceView{
							Statuses: []*armcompute.InstanceViewStatus{
								{
									Code: to.Ptr("PowerState/Running"),
								},
							},
						},
					},
				},
				{
					ID:   to.Ptr("VMWithMultipleNics"),
					Name: to.Ptr("VMWithMultipleNics"),
					Tags: map[string]*string{
						"poolName": to.Ptr("poolName"),
					},
					Properties: &armcompute.VirtualMachineProperties{
						NetworkProfile: &armcompute.NetworkProfile{
							NetworkInterfaces: []*armcompute.NetworkInterfaceReference{
								{
									ID: to.Ptr("VMWithMultipleNics1"),
									Properties: &armcompute.NetworkInterfaceReferenceProperties{
										Primary: to.Ptr(true),
									},
								},
								{
									ID: to.Ptr("VMWithMultipleNics2"),
								},
							},
						},
						InstanceView: &armcompute.VirtualMachineInstanceView{
							Statuses: []*armcompute.InstanceViewStatus{
								{
									Code: to.Ptr("PowerState/Running"),
								},
							},
						},
					},
				},
			}, nil)
			nicMap = map[string]struct{}{
				strings.ToLower("VMWithMultipleNics1"): {},
				strings.ToLower("VMWithSingleNic1"):    {},
			}
		})
		When("DecoupleLBBackendPool is invoked", func() {
			It("should invoke nic reconciler", func() {
				nicReconciler.EXPECT().DecoupleNicInterfaceWithLBBackendpool(gomock.Any(), gomock.Any(), gomock.Any(), nicMap, backendpoolIDs, backendpoolIDsIPV6).Return(nil)
				err := reconciler.DecoupleLBBackendPool(ctx, computeSubscriptionID, networkSubscriptionID, resourceGroupName, backendpoolIDs, backendpoolIDsIPV6)
				Expect(err).To(BeNil())
			})
		})
		When("DecoupleLBBackendPool is invoked", func() {
			It("should invoke nic reconciler", func() {
				nicReconciler.EXPECT().DecoupleNicInterfaceWithLBBackendpool(gomock.Any(), gomock.Any(), gomock.Any(), nicMap, backendpoolIDs, backendpoolIDsIPV6).Return(cgerror.CreateInternalError())
				err := reconciler.DecoupleLBBackendPool(ctx, computeSubscriptionID, networkSubscriptionID, resourceGroupName, backendpoolIDs, backendpoolIDsIPV6)
				Expect(err).NotTo(BeNil())
			})
		})
	})

	Context("AssociateLBBackendpool", func() {
		var nicMap map[string]struct{}
		BeforeEach(func() {
			vmClient.EXPECT().ListAll(testlib.IsContext(), gomock.Any(), azureresources_test.ListVMWithInstanceViewAndARG()).Return([]*armcompute.VirtualMachine{
				{
					ID:   to.Ptr("VMWithSingleNic"),
					Name: to.Ptr("VMWithSingleNic"),
					Tags: map[string]*string{
						"poolName": to.Ptr("poolName"),
					},
					Properties: &armcompute.VirtualMachineProperties{
						NetworkProfile: &armcompute.NetworkProfile{
							NetworkInterfaces: []*armcompute.NetworkInterfaceReference{
								{
									ID: to.Ptr("VMWithSingleNic1"),
								},
							},
						},
						InstanceView: &armcompute.VirtualMachineInstanceView{
							Statuses: []*armcompute.InstanceViewStatus{
								{
									Code: to.Ptr("PowerState/Running"),
								},
							},
						},
					},
				},
				{
					ID:   to.Ptr("VMWithMultipleNics"),
					Name: to.Ptr("VMWithMultipleNics"),
					Tags: map[string]*string{
						"poolName": to.Ptr("poolName"),
					},
					Properties: &armcompute.VirtualMachineProperties{
						NetworkProfile: &armcompute.NetworkProfile{
							NetworkInterfaces: []*armcompute.NetworkInterfaceReference{
								{
									ID: to.Ptr("VMWithMultipleNics1"),
									Properties: &armcompute.NetworkInterfaceReferenceProperties{
										Primary: to.Ptr(true),
									},
								},
								{
									ID: to.Ptr("VMWithMultipleNics2"),
								},
							},
						},
						InstanceView: &armcompute.VirtualMachineInstanceView{
							Statuses: []*armcompute.InstanceViewStatus{
								{
									Code: to.Ptr("PowerState/Running"),
								},
							},
						},
					},
				},
			}, nil)
			nicMap = map[string]struct{}{
				strings.ToLower("VMWithMultipleNics1"): {},
				strings.ToLower("VMWithSingleNic1"):    {},
			}
		})
		When("AssociateLBBackendpool is invoked", func() {
			It("should invoke nic reconciler", func() {
				nicReconciler.EXPECT().AssociateNicInterfaceWithLBBackendpool(gomock.Any(), gomock.Any(), gomock.Any(), nicMap, backendpoolIDs, backendpoolIDsIPV6).Return(nil)
				err := reconciler.AssociateLBBackendpool(ctx, computeSubscriptionID, networkSubscriptionID, resourceGroupName, backendpoolIDs, backendpoolIDsIPV6, nil)
				Expect(err).To(BeNil())
			})
		})
		When("AssociateLBBackendpool is invoked", func() {
			It("should invoke nic reconciler", func() {
				nicReconciler.EXPECT().AssociateNicInterfaceWithLBBackendpool(gomock.Any(), gomock.Any(), gomock.Any(), nicMap, backendpoolIDs, backendpoolIDsIPV6).Return(cgerror.CreateInternalError())
				err := reconciler.AssociateLBBackendpool(ctx, computeSubscriptionID, networkSubscriptionID, resourceGroupName, backendpoolIDs, backendpoolIDsIPV6, nil)
				Expect(err).NotTo(BeNil())
			})
		})
	})
})
