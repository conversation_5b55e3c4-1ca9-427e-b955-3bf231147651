package vm

import (
	"context"
	"fmt"
	"strings"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore/to"
	"github.com/Azure/azure-sdk-for-go/services/compute/mgmt/2022-03-01/compute"
	. "github.com/onsi/ginkgo"
	ginkgotable "github.com/onsi/ginkgo/extensions/table"
	. "github.com/onsi/gomega"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool/vm/nic/mock_nic"
	"go.goms.io/aks/rp/toolkit/azureclients/vmclient/mock_vmclient"
	cgerror "go.goms.io/aks/rp/toolkit/categorizederror"
	gomock "go.uber.org/mock/gomock"
)

var _ = Describe("Reconciler", func() {
	var mockCtrl *gomock.Controller
	var vmClient *mock_vmclient.MockInterface
	var nicReconciler *mock_nic.MockNicInterfaceReconciler
	var reconciler *VMAgentpoolBackendpoolReconciler

	var (
		backendpoolIDs     map[string]struct{}
		backendpoolIDsIPV6 map[string]struct{}
		// Test backend pool IDs for exclusion testing
		inboundBackendPoolID      string
		inboundBackendPoolIDIPv6  string
		outboundBackendPoolID     string
		outboundBackendPoolIDIPv6 string
	)

	BeforeEach(func() {
		mockCtrl = gomock.NewController(GinkgoT())
		vmClient = mock_vmclient.NewMockInterface(mockCtrl)
		nicReconciler = mock_nic.NewMockNicInterfaceReconciler(mockCtrl)
		reconciler = &VMAgentpoolBackendpoolReconciler{
			vmClient:      vmClient,
			nicReconciler: nicReconciler,
		}

		// Initialize test backend pool IDs
		inboundBackendPoolID = "/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Network/loadBalancers/kubernetes/backendAddressPools/kubernetes"
		inboundBackendPoolIDIPv6 = "/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Network/loadBalancers/kubernetes/backendAddressPools/kubernetes-ipv6"
		outboundBackendPoolID = "/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Network/loadBalancers/kubernetes/backendAddressPools/aksOutboundBackendPool"
		outboundBackendPoolIDIPv6 = "/subscriptions/test-sub/resourceGroups/test-rg/providers/Microsoft.Network/loadBalancers/kubernetes/backendAddressPools/aksOutboundBackendPool-ipv6"
	})
	AfterEach(func() {
		mockCtrl.Finish()
	})

	Context("listAllOfVMInterfaces", func() {
		var (
			VMWithSingleNic = compute.VirtualMachine{
				ID:   to.Ptr("VMWithSingleNic"),
				Name: to.Ptr("VMWithSingleNic"),
				Tags: map[string]*string{
					"poolName": to.Ptr("poolName"),
				},
				VirtualMachineProperties: &compute.VirtualMachineProperties{
					NetworkProfile: &compute.NetworkProfile{
						NetworkInterfaces: &[]compute.NetworkInterfaceReference{
							{
								ID: to.Ptr("VMWithSingleNic1"),
							},
						},
					},
					InstanceView: &compute.VirtualMachineInstanceView{
						Statuses: &[]compute.InstanceViewStatus{
							{
								Code: to.Ptr("PowerState/Running"),
							},
						},
					},
				},
			}
			VMWithoutInstanceView = compute.VirtualMachine{
				ID:   to.Ptr("VMWithoutInstanceView"),
				Name: to.Ptr("VMWithoutInstanceView"),
				Tags: map[string]*string{
					"poolName": to.Ptr("poolName"),
				},
				VirtualMachineProperties: &compute.VirtualMachineProperties{
					NetworkProfile: &compute.NetworkProfile{
						NetworkInterfaces: &[]compute.NetworkInterfaceReference{
							{
								ID: to.Ptr("VMWithoutInstanceView"),
							},
						},
					},
				},
			}
			VMWithMultipleNics = compute.VirtualMachine{
				ID:   to.Ptr("VMWithMultipleNics"),
				Name: to.Ptr("VMWithMultipleNics"),
				Tags: map[string]*string{
					"poolName": to.Ptr("poolName"),
				},
				VirtualMachineProperties: &compute.VirtualMachineProperties{
					NetworkProfile: &compute.NetworkProfile{
						NetworkInterfaces: &[]compute.NetworkInterfaceReference{
							{
								ID: to.Ptr("VMWithMultipleNics1"),
								NetworkInterfaceReferenceProperties: &compute.NetworkInterfaceReferenceProperties{
									Primary: to.Ptr(true),
								},
							},
							{
								ID: to.Ptr("VMWithMultipleNics2"),
							},
						},
					},
					InstanceView: &compute.VirtualMachineInstanceView{
						Statuses: &[]compute.InstanceViewStatus{
							{
								Code: to.Ptr("PowerState/Running"),
							},
						},
					},
				},
			}
			UnmanagedVM = compute.VirtualMachine{
				ID:   to.Ptr("UnmanagedVM"),
				Name: to.Ptr("UnmanagedVM"),
				VirtualMachineProperties: &compute.VirtualMachineProperties{
					NetworkProfile: &compute.NetworkProfile{
						NetworkInterfaces: &[]compute.NetworkInterfaceReference{
							{
								ID: to.Ptr("UnmanagedVM"),
								NetworkInterfaceReferenceProperties: &compute.NetworkInterfaceReferenceProperties{
									Primary: to.Ptr(true),
								},
							},
							{
								ID: to.Ptr("UnmanagedVM"),
							},
						},
					},
					InstanceView: &compute.VirtualMachineInstanceView{
						Statuses: &[]compute.InstanceViewStatus{
							{
								Code: to.Ptr("PowerState/Running"),
							},
						},
					},
				},
			}
			StoppedVM = compute.VirtualMachine{
				ID:   to.Ptr("StoppedVM"),
				Name: to.Ptr("StoppedVM"),
				Tags: map[string]*string{
					"poolName": to.Ptr("poolName"),
				},
				VirtualMachineProperties: &compute.VirtualMachineProperties{
					NetworkProfile: &compute.NetworkProfile{
						NetworkInterfaces: &[]compute.NetworkInterfaceReference{
							{
								ID: to.Ptr("StoppedVM"),
								NetworkInterfaceReferenceProperties: &compute.NetworkInterfaceReferenceProperties{
									Primary: to.Ptr(true),
								},
							},
							{
								ID: to.Ptr("StoppedVM"),
							},
						},
					},
					InstanceView: &compute.VirtualMachineInstanceView{
						Statuses: &[]compute.InstanceViewStatus{
							{
								Code: to.Ptr("PowerState/Stopped"),
							},
						},
					},
				},
			}
			invalidVMWithMultipleNics = compute.VirtualMachine{
				ID:   to.Ptr("invalidVMWithMultipleNics"),
				Name: to.Ptr("invalidVMWithMultipleNics"),
				Tags: map[string]*string{
					"poolName": to.Ptr("poolName"),
				},
				VirtualMachineProperties: &compute.VirtualMachineProperties{
					NetworkProfile: &compute.NetworkProfile{
						NetworkInterfaces: &[]compute.NetworkInterfaceReference{
							{
								ID: to.Ptr("VMWithMultipleNics1"),
							},
							{
								ID: to.Ptr("VMWithMultipleNics2"),
							},
						},
					},
					InstanceView: &compute.VirtualMachineInstanceView{
						Statuses: &[]compute.InstanceViewStatus{
							{
								Code: to.Ptr("PowerState/Running"),
							},
						},
					},
				},
			}
			vmInterminatingstate = compute.VirtualMachine{
				ID:   to.Ptr("invalidVMWithMultipleNics"),
				Name: to.Ptr("invalidVMWithMultipleNics"),
				Tags: map[string]*string{
					"poolName": to.Ptr("poolName"),
				},
				VirtualMachineProperties: &compute.VirtualMachineProperties{
					NetworkProfile: &compute.NetworkProfile{
						NetworkInterfaces: &[]compute.NetworkInterfaceReference{
							{
								ID: to.Ptr("VMWithMultipleNics1"),
							},
							{
								ID: to.Ptr("VMWithMultipleNics2"),
							},
						},
					},
					ProvisioningState: to.Ptr("Deallocating"),
					InstanceView: &compute.VirtualMachineInstanceView{
						Statuses: &[]compute.InstanceViewStatus{
							{
								Code: to.Ptr("PowerState/Running"),
							},
						},
					},
				},
			}
		)
		BeforeEach(func() {
			VMWithoutInstanceView.InstanceView = nil
		})

		ginkgotable.DescribeTable("listAllOfVMInterfaces validation table", func(vmLists []compute.VirtualMachine, generatedListError *cgerror.CategorizedError, generatedGetError *cgerror.CategorizedError, expectedIDMap map[string]struct{}, expectedErr error) {
			mockCtrl := gomock.NewController(GinkgoT())
			defer mockCtrl.Finish()
			vmClient = mock_vmclient.NewMockInterface(mockCtrl)
			nicReconciler = mock_nic.NewMockNicInterfaceReconciler(mockCtrl)
			reconciler = &VMAgentpoolBackendpoolReconciler{
				vmClient:      vmClient,
				nicReconciler: nicReconciler,
			}

			vmClient.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return(vmLists, nil, generatedListError)
			vmClient.EXPECT().GetVirtualMachineInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&compute.VirtualMachineInstanceView{
				Statuses: &[]compute.InstanceViewStatus{
					{
						Code: to.Ptr("PowerState/Running"),
					},
				},
			}, generatedGetError).AnyTimes()
			nicList, err := reconciler.listAllOfVMInterfaces(context.Background(), computeSubscriptionID, resourceGroupName)
			if expectedErr != nil {
				Expect(err).To(BeEquivalentTo(expectedErr))
			} else {
				Expect(err).To(BeNil())
			}
			Expect(nicList).To(BeEquivalentTo(expectedIDMap))
		},
			ginkgotable.Entry("vmlist is nil", nil, nil, nil, nil, nil),
			ginkgotable.Entry("vmclient returned error", nil, cgerror.ToCategorizedError(fmt.Errorf("failed to return list resuilt")), nil, nil, cgerror.ToCategorizedError(fmt.Errorf("failed to return list resuilt"))),
			ginkgotable.Entry("vmclient returned 1 valid vm", []compute.VirtualMachine{
				VMWithSingleNic,
			}, nil, nil, map[string]struct{}{
				strings.ToLower("VMWithSingleNic1"): {},
			}, nil),
			ginkgotable.Entry("vmclient returned 1 valid vm", []compute.VirtualMachine{
				VMWithoutInstanceView,
			}, nil, nil, map[string]struct{}{
				strings.ToLower("VMWithoutInstanceView"): {},
			}, nil),
			ginkgotable.Entry("vmclient returned 1 valid vm", []compute.VirtualMachine{
				VMWithoutInstanceView,
			}, nil, cgerror.CreateInternalError(), nil, cgerror.CreateInternalError()),
			ginkgotable.Entry("vmclient returned 1 valid vm with multiple nics ", []compute.VirtualMachine{
				VMWithMultipleNics,
			}, nil, nil, map[string]struct{}{
				strings.ToLower("VMWithMultipleNics1"): {},
			}, nil),
			ginkgotable.Entry("vmclient returned 1 invalid vm with multiple nics ", []compute.VirtualMachine{
				invalidVMWithMultipleNics,
			}, nil, nil, nil, nil),
			ginkgotable.Entry("vmclient returned multiple vms including 1 invalid vm", []compute.VirtualMachine{
				invalidVMWithMultipleNics,
				VMWithMultipleNics,
				VMWithSingleNic,
			}, nil, nil, map[string]struct{}{
				strings.ToLower("VMWithMultipleNics1"): {},
				strings.ToLower("VMWithSingleNic1"):    {},
			}, nil),
			ginkgotable.Entry("vmclient returned 1 unmanaged vm", []compute.VirtualMachine{
				UnmanagedVM,
			}, nil, nil, nil, nil),
			ginkgotable.Entry("vmclient returned 1 vm in terminating state", []compute.VirtualMachine{
				vmInterminatingstate,
			}, nil, nil, nil, nil),
			ginkgotable.Entry("vmclient returned 1 vm in stopped state", []compute.VirtualMachine{
				StoppedVM,
			}, nil, nil, nil, nil),
			ginkgotable.Entry("vmclient returned 1 vm without instance view", []compute.VirtualMachine{
				{
					ID:   to.Ptr("VMWithSingleNic"),
					Name: to.Ptr("VMWithSingleNic"),
					Tags: map[string]*string{
						"poolName": to.Ptr("poolName"),
					},
					VirtualMachineProperties: &compute.VirtualMachineProperties{
						NetworkProfile: &compute.NetworkProfile{
							NetworkInterfaces: &[]compute.NetworkInterfaceReference{
								{
									ID: to.Ptr("VMWithSingleNic1"),
								},
							},
						},
					},
				},
			}, nil, nil, map[string]struct{}{
				strings.ToLower("VMWithSingleNic1"): {},
			}, nil),
			ginkgotable.Entry("vmclient returned invalid vm", []compute.VirtualMachine{
				{
					ID:   to.Ptr("nilVirtualMachineProperties"),
					Name: to.Ptr("invalidVMWithMultipleNics"),
				},
				{
					ID:                       to.Ptr("nilNetworkProfile"),
					Name:                     to.Ptr("invalidVMWithMultipleNics"),
					VirtualMachineProperties: &compute.VirtualMachineProperties{},
				},
				{
					ID:   to.Ptr("nilNetworkInterfaces"),
					Name: to.Ptr("invalidVMWithMultipleNics"),
					VirtualMachineProperties: &compute.VirtualMachineProperties{
						NetworkProfile: &compute.NetworkProfile{},
					},
				},
				{
					ID:   to.Ptr("nilNetworkInterfacesID"),
					Name: to.Ptr("invalidVMWithMultipleNics"),
					VirtualMachineProperties: &compute.VirtualMachineProperties{
						NetworkProfile: &compute.NetworkProfile{
							NetworkInterfaces: &[]compute.NetworkInterfaceReference{
								{},
							},
						},
					},
				},
			}, nil, nil, nil, nil),
		)
	})

	Context("DecoupleLBBackendPool", func() {
		var nicMap map[string]struct{}
		BeforeEach(func() {
			vmClient.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]compute.VirtualMachine{
				{
					ID:   to.Ptr("VMWithSingleNic"),
					Name: to.Ptr("VMWithSingleNic"),
					Tags: map[string]*string{
						"poolName": to.Ptr("poolName"),
					},
					VirtualMachineProperties: &compute.VirtualMachineProperties{
						NetworkProfile: &compute.NetworkProfile{
							NetworkInterfaces: &[]compute.NetworkInterfaceReference{
								{
									ID: to.Ptr("VMWithSingleNic1"),
								},
							},
						},
						InstanceView: &compute.VirtualMachineInstanceView{
							Statuses: &[]compute.InstanceViewStatus{
								{
									Code: to.Ptr("PowerState/Running"),
								},
							},
						},
					},
				},
				{
					ID:   to.Ptr("VMWithMultipleNics"),
					Name: to.Ptr("VMWithMultipleNics"),
					Tags: map[string]*string{
						"poolName": to.Ptr("poolName"),
					},
					VirtualMachineProperties: &compute.VirtualMachineProperties{
						NetworkProfile: &compute.NetworkProfile{
							NetworkInterfaces: &[]compute.NetworkInterfaceReference{
								{
									ID: to.Ptr("VMWithMultipleNics1"),
									NetworkInterfaceReferenceProperties: &compute.NetworkInterfaceReferenceProperties{
										Primary: to.Ptr(true),
									},
								},
								{
									ID: to.Ptr("VMWithMultipleNics2"),
								},
							},
						},
						InstanceView: &compute.VirtualMachineInstanceView{
							Statuses: &[]compute.InstanceViewStatus{
								{
									Code: to.Ptr("PowerState/Running"),
								},
							},
						},
					},
				},
			}, nil, nil)
			nicMap = map[string]struct{}{
				strings.ToLower("VMWithMultipleNics1"): {},
				strings.ToLower("VMWithSingleNic1"):    {},
			}
		})
		When("DecoupleLBBackendPool is invoked", func() {
			It("should invoke nic reconciler", func() {
				nicReconciler.EXPECT().DecoupleNicInterfaceWithLBBackendpool(gomock.Any(), gomock.Any(), gomock.Any(), nicMap, backendpoolIDs, backendpoolIDsIPV6).Return(nil)
				err := reconciler.DecoupleLBBackendPool(context.Background(), computeSubscriptionID, networkSubscriptionID, resourceGroupName, backendpoolIDs, backendpoolIDsIPV6)
				Expect(err).To(BeNil())
			})
		})
		When("DecoupleLBBackendPool is invoked", func() {
			It("should invoke nic reconciler", func() {
				nicReconciler.EXPECT().DecoupleNicInterfaceWithLBBackendpool(gomock.Any(), gomock.Any(), gomock.Any(), nicMap, backendpoolIDs, backendpoolIDsIPV6).Return(cgerror.CreateInternalError())
				err := reconciler.DecoupleLBBackendPool(context.Background(), computeSubscriptionID, networkSubscriptionID, resourceGroupName, backendpoolIDs, backendpoolIDsIPV6)
				Expect(err).NotTo(BeNil())
			})
		})
	})

	Context("AssociateLBBackendpool", func() {
		var nicMap map[string]struct{}
		BeforeEach(func() {
			vmClient.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]compute.VirtualMachine{
				{
					ID:   to.Ptr("VMWithSingleNic"),
					Name: to.Ptr("VMWithSingleNic"),
					Tags: map[string]*string{
						"poolName": to.Ptr("poolName"),
					},
					VirtualMachineProperties: &compute.VirtualMachineProperties{
						NetworkProfile: &compute.NetworkProfile{
							NetworkInterfaces: &[]compute.NetworkInterfaceReference{
								{
									ID: to.Ptr("VMWithSingleNic1"),
								},
							},
						},
						InstanceView: &compute.VirtualMachineInstanceView{
							Statuses: &[]compute.InstanceViewStatus{
								{
									Code: to.Ptr("PowerState/Running"),
								},
							},
						},
					},
				},
				{
					ID:   to.Ptr("VMWithMultipleNics"),
					Name: to.Ptr("VMWithMultipleNics"),
					Tags: map[string]*string{
						"poolName": to.Ptr("poolName"),
					},
					VirtualMachineProperties: &compute.VirtualMachineProperties{
						NetworkProfile: &compute.NetworkProfile{
							NetworkInterfaces: &[]compute.NetworkInterfaceReference{
								{
									ID: to.Ptr("VMWithMultipleNics1"),
									NetworkInterfaceReferenceProperties: &compute.NetworkInterfaceReferenceProperties{
										Primary: to.Ptr(true),
									},
								},
								{
									ID: to.Ptr("VMWithMultipleNics2"),
								},
							},
						},
						InstanceView: &compute.VirtualMachineInstanceView{
							Statuses: &[]compute.InstanceViewStatus{
								{
									Code: to.Ptr("PowerState/Running"),
								},
							},
						},
					},
				},
			}, nil, nil)
			nicMap = map[string]struct{}{
				strings.ToLower("VMWithMultipleNics1"): {},
				strings.ToLower("VMWithSingleNic1"):    {},
			}
		})
		When("AssociateLBBackendpool is invoked with nil excludedAgentPoolNames", func() {
			It("should invoke nic reconciler for all VMs", func() {
				nicReconciler.EXPECT().AssociateNicInterfaceWithLBBackendpool(gomock.Any(), gomock.Any(), gomock.Any(), nicMap, backendpoolIDs, backendpoolIDsIPV6).Return(nil)
				err := reconciler.AssociateLBBackendpool(context.Background(), computeSubscriptionID, networkSubscriptionID, resourceGroupName, backendpoolIDs, backendpoolIDsIPV6, nil)
				Expect(err).To(BeNil())
			})
		})
		When("AssociateLBBackendpool is invoked and returns error", func() {
			It("should return error", func() {
				nicReconciler.EXPECT().AssociateNicInterfaceWithLBBackendpool(gomock.Any(), gomock.Any(), gomock.Any(), nicMap, backendpoolIDs, backendpoolIDsIPV6).Return(cgerror.CreateInternalError())
				err := reconciler.AssociateLBBackendpool(context.Background(), computeSubscriptionID, networkSubscriptionID, resourceGroupName, backendpoolIDs, backendpoolIDsIPV6, nil)
				Expect(err).NotTo(BeNil())
			})
		})

		Context("AssociateLBBackendpool with exclusion functionality", func() {
			var (
				mockCtrlExclusion      *gomock.Controller
				vmClientExclusion      *mock_vmclient.MockInterface
				nicReconcilerExclusion *mock_nic.MockNicInterfaceReconciler
				reconcilerExclusion    *VMAgentpoolBackendpoolReconciler
				excludedAgentPoolNames map[string]struct{}
				normalVMNicMap         map[string]struct{}
				excludedVMNicMap       map[string]struct{}
				allBackendPools        map[string]struct{}
				allBackendPoolsIPv6    map[string]struct{}
				outboundOnlyPools      map[string]struct{}
				outboundOnlyPoolsIPv6  map[string]struct{}
			)

			BeforeEach(func() {
				// Create separate mocks for exclusion tests to avoid interference
				mockCtrlExclusion = gomock.NewController(GinkgoT())
				vmClientExclusion = mock_vmclient.NewMockInterface(mockCtrlExclusion)
				nicReconcilerExclusion = mock_nic.NewMockNicInterfaceReconciler(mockCtrlExclusion)
				reconcilerExclusion = &VMAgentpoolBackendpoolReconciler{
					vmClient:      vmClientExclusion,
					nicReconciler: nicReconcilerExclusion,
				}

				// Setup excluded agent pool names
				excludedAgentPoolNames = map[string]struct{}{
					"excludedPool1": {},
					"excludedPool2": {},
				}

				// Setup backend pools with both inbound and outbound pools
				allBackendPools = map[string]struct{}{
					inboundBackendPoolID:  {},
					outboundBackendPoolID: {},
				}
				allBackendPoolsIPv6 = map[string]struct{}{
					inboundBackendPoolIDIPv6:  {},
					outboundBackendPoolIDIPv6: {},
				}

				// Setup outbound-only pools (what excluded VMs should get)
				outboundOnlyPools = map[string]struct{}{
					outboundBackendPoolID: {},
				}
				outboundOnlyPoolsIPv6 = map[string]struct{}{
					outboundBackendPoolIDIPv6: {},
				}

				normalVMNicMap = map[string]struct{}{
					strings.ToLower("NormalVM1-nic"): {},
					strings.ToLower("NormalVM2-nic"): {},
				}
				excludedVMNicMap = map[string]struct{}{
					strings.ToLower("ExcludedVM1-nic"): {},
					strings.ToLower("ExcludedVM2-nic"): {},
				}
			})

			AfterEach(func() {
				mockCtrlExclusion.Finish()
			})

			When("VMs have mixed normal and excluded agent pools", func() {
				BeforeEach(func() {
					vmClientExclusion.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]compute.VirtualMachine{
						// Normal VMs (not excluded)
						{
							ID:   to.Ptr("NormalVM1"),
							Name: to.Ptr("NormalVM1"),
							Tags: map[string]*string{
								"poolName": to.Ptr("normalPool1"),
							},
							VirtualMachineProperties: &compute.VirtualMachineProperties{
								NetworkProfile: &compute.NetworkProfile{
									NetworkInterfaces: &[]compute.NetworkInterfaceReference{
										{
											ID: to.Ptr("NormalVM1-nic"),
										},
									},
								},
								InstanceView: &compute.VirtualMachineInstanceView{
									Statuses: &[]compute.InstanceViewStatus{
										{
											Code: to.Ptr("PowerState/Running"),
										},
									},
								},
							},
						},
						{
							ID:   to.Ptr("NormalVM2"),
							Name: to.Ptr("NormalVM2"),
							Tags: map[string]*string{
								"poolName": to.Ptr("normalPool2"),
							},
							VirtualMachineProperties: &compute.VirtualMachineProperties{
								NetworkProfile: &compute.NetworkProfile{
									NetworkInterfaces: &[]compute.NetworkInterfaceReference{
										{
											ID: to.Ptr("NormalVM2-nic"),
										},
									},
								},
								InstanceView: &compute.VirtualMachineInstanceView{
									Statuses: &[]compute.InstanceViewStatus{
										{
											Code: to.Ptr("PowerState/Running"),
										},
									},
								},
							},
						},
						// Excluded VMs
						{
							ID:   to.Ptr("ExcludedVM1"),
							Name: to.Ptr("ExcludedVM1"),
							Tags: map[string]*string{
								"poolName": to.Ptr("excludedPool1"),
							},
							VirtualMachineProperties: &compute.VirtualMachineProperties{
								NetworkProfile: &compute.NetworkProfile{
									NetworkInterfaces: &[]compute.NetworkInterfaceReference{
										{
											ID: to.Ptr("ExcludedVM1-nic"),
										},
									},
								},
								InstanceView: &compute.VirtualMachineInstanceView{
									Statuses: &[]compute.InstanceViewStatus{
										{
											Code: to.Ptr("PowerState/Running"),
										},
									},
								},
							},
						},
						{
							ID:   to.Ptr("ExcludedVM2"),
							Name: to.Ptr("ExcludedVM2"),
							Tags: map[string]*string{
								"poolName": to.Ptr("excludedPool2"),
							},
							VirtualMachineProperties: &compute.VirtualMachineProperties{
								NetworkProfile: &compute.NetworkProfile{
									NetworkInterfaces: &[]compute.NetworkInterfaceReference{
										{
											ID: to.Ptr("ExcludedVM2-nic"),
										},
									},
								},
								InstanceView: &compute.VirtualMachineInstanceView{
									Statuses: &[]compute.InstanceViewStatus{
										{
											Code: to.Ptr("PowerState/Running"),
										},
									},
								},
							},
						},
					}, nil, nil)
				})

				It("should call nic reconciler twice - once for normal VMs with all pools, once for excluded VMs with outbound pools only", func() {
					// Expect call for normal VMs with all backend pools
					nicReconcilerExclusion.EXPECT().AssociateNicInterfaceWithLBBackendpool(
						gomock.Any(), gomock.Any(), gomock.Any(),
						normalVMNicMap, allBackendPools, allBackendPoolsIPv6,
					).Return(nil)

					// Expect call for excluded VMs with outbound pools only
					nicReconcilerExclusion.EXPECT().AssociateNicInterfaceWithLBBackendpool(
						gomock.Any(), gomock.Any(), gomock.Any(),
						excludedVMNicMap, outboundOnlyPools, outboundOnlyPoolsIPv6,
					).Return(nil)

					err := reconcilerExclusion.AssociateLBBackendpool(
						context.Background(), computeSubscriptionID, networkSubscriptionID, resourceGroupName,
						allBackendPools, allBackendPoolsIPv6, excludedAgentPoolNames,
					)
					Expect(err).To(BeNil())
				})
			})

			When("all VMs are from excluded agent pools", func() {
				BeforeEach(func() {
					vmClientExclusion.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]compute.VirtualMachine{
						{
							ID:   to.Ptr("ExcludedVM1"),
							Name: to.Ptr("ExcludedVM1"),
							Tags: map[string]*string{
								"poolName": to.Ptr("excludedPool1"),
							},
							VirtualMachineProperties: &compute.VirtualMachineProperties{
								NetworkProfile: &compute.NetworkProfile{
									NetworkInterfaces: &[]compute.NetworkInterfaceReference{
										{
											ID: to.Ptr("ExcludedVM1-nic"),
										},
									},
								},
								InstanceView: &compute.VirtualMachineInstanceView{
									Statuses: &[]compute.InstanceViewStatus{
										{
											Code: to.Ptr("PowerState/Running"),
										},
									},
								},
							},
						},
					}, nil, nil)
				})

				It("should only call nic reconciler for excluded VMs with outbound pools", func() {
					excludedNicMap := map[string]struct{}{
						strings.ToLower("ExcludedVM1-nic"): {},
					}

					// Should only be called once for excluded VMs with outbound pools
					nicReconcilerExclusion.EXPECT().AssociateNicInterfaceWithLBBackendpool(
						gomock.Any(), gomock.Any(), gomock.Any(),
						excludedNicMap, outboundOnlyPools, outboundOnlyPoolsIPv6,
					).Return(nil)

					err := reconcilerExclusion.AssociateLBBackendpool(
						context.Background(), computeSubscriptionID, networkSubscriptionID, resourceGroupName,
						allBackendPools, allBackendPoolsIPv6, excludedAgentPoolNames,
					)
					Expect(err).To(BeNil())
				})
			})

			When("all VMs are from normal agent pools", func() {
				BeforeEach(func() {
					vmClientExclusion.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]compute.VirtualMachine{
						{
							ID:   to.Ptr("NormalVM1"),
							Name: to.Ptr("NormalVM1"),
							Tags: map[string]*string{
								"poolName": to.Ptr("normalPool1"),
							},
							VirtualMachineProperties: &compute.VirtualMachineProperties{
								NetworkProfile: &compute.NetworkProfile{
									NetworkInterfaces: &[]compute.NetworkInterfaceReference{
										{
											ID: to.Ptr("NormalVM1-nic"),
										},
									},
								},
								InstanceView: &compute.VirtualMachineInstanceView{
									Statuses: &[]compute.InstanceViewStatus{
										{
											Code: to.Ptr("PowerState/Running"),
										},
									},
								},
							},
						},
					}, nil, nil)
				})

				It("should only call nic reconciler for normal VMs with all pools", func() {
					normalNicMap := map[string]struct{}{
						strings.ToLower("NormalVM1-nic"): {},
					}

					// Should only be called once for normal VMs with all pools
					nicReconcilerExclusion.EXPECT().AssociateNicInterfaceWithLBBackendpool(
						gomock.Any(), gomock.Any(), gomock.Any(),
						normalNicMap, allBackendPools, allBackendPoolsIPv6,
					).Return(nil)

					err := reconcilerExclusion.AssociateLBBackendpool(
						context.Background(), computeSubscriptionID, networkSubscriptionID, resourceGroupName,
						allBackendPools, allBackendPoolsIPv6, excludedAgentPoolNames,
					)
					Expect(err).To(BeNil())
				})
			})

			When("excludedAgentPoolNames is empty", func() {
				BeforeEach(func() {
					excludedAgentPoolNames = map[string]struct{}{}
					vmClientExclusion.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]compute.VirtualMachine{
						{
							ID:   to.Ptr("VM1"),
							Name: to.Ptr("VM1"),
							Tags: map[string]*string{
								"poolName": to.Ptr("anyPool"),
							},
							VirtualMachineProperties: &compute.VirtualMachineProperties{
								NetworkProfile: &compute.NetworkProfile{
									NetworkInterfaces: &[]compute.NetworkInterfaceReference{
										{
											ID: to.Ptr("VM1-nic"),
										},
									},
								},
								InstanceView: &compute.VirtualMachineInstanceView{
									Statuses: &[]compute.InstanceViewStatus{
										{
											Code: to.Ptr("PowerState/Running"),
										},
									},
								},
							},
						},
					}, nil, nil)
				})

				It("should treat all VMs as normal and call nic reconciler with all pools", func() {
					allNicMap := map[string]struct{}{
						strings.ToLower("VM1-nic"): {},
					}

					nicReconcilerExclusion.EXPECT().AssociateNicInterfaceWithLBBackendpool(
						gomock.Any(), gomock.Any(), gomock.Any(),
						allNicMap, allBackendPools, allBackendPoolsIPv6,
					).Return(nil)

					err := reconcilerExclusion.AssociateLBBackendpool(
						context.Background(), computeSubscriptionID, networkSubscriptionID, resourceGroupName,
						allBackendPools, allBackendPoolsIPv6, excludedAgentPoolNames,
					)
					Expect(err).To(BeNil())
				})
			})

			When("error occurs during normal VM processing", func() {
				BeforeEach(func() {
					vmClientExclusion.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]compute.VirtualMachine{
						{
							ID:   to.Ptr("NormalVM1"),
							Name: to.Ptr("NormalVM1"),
							Tags: map[string]*string{
								"poolName": to.Ptr("normalPool1"),
							},
							VirtualMachineProperties: &compute.VirtualMachineProperties{
								NetworkProfile: &compute.NetworkProfile{
									NetworkInterfaces: &[]compute.NetworkInterfaceReference{
										{
											ID: to.Ptr("NormalVM1-nic"),
										},
									},
								},
								InstanceView: &compute.VirtualMachineInstanceView{
									Statuses: &[]compute.InstanceViewStatus{
										{
											Code: to.Ptr("PowerState/Running"),
										},
									},
								},
							},
						},
					}, nil, nil)
				})

				It("should return error from normal VM processing", func() {
					normalNicMap := map[string]struct{}{
						strings.ToLower("NormalVM1-nic"): {},
					}
					expectedError := cgerror.CreateInternalError()

					nicReconcilerExclusion.EXPECT().AssociateNicInterfaceWithLBBackendpool(
						gomock.Any(), gomock.Any(), gomock.Any(),
						normalNicMap, allBackendPools, allBackendPoolsIPv6,
					).Return(expectedError)

					err := reconcilerExclusion.AssociateLBBackendpool(
						context.Background(), computeSubscriptionID, networkSubscriptionID, resourceGroupName,
						allBackendPools, allBackendPoolsIPv6, excludedAgentPoolNames,
					)
					Expect(err).To(Equal(expectedError))
				})
			})

			When("error occurs during excluded VM processing", func() {
				BeforeEach(func() {
					vmClientExclusion.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]compute.VirtualMachine{
						{
							ID:   to.Ptr("ExcludedVM1"),
							Name: to.Ptr("ExcludedVM1"),
							Tags: map[string]*string{
								"poolName": to.Ptr("excludedPool1"),
							},
							VirtualMachineProperties: &compute.VirtualMachineProperties{
								NetworkProfile: &compute.NetworkProfile{
									NetworkInterfaces: &[]compute.NetworkInterfaceReference{
										{
											ID: to.Ptr("ExcludedVM1-nic"),
										},
									},
								},
								InstanceView: &compute.VirtualMachineInstanceView{
									Statuses: &[]compute.InstanceViewStatus{
										{
											Code: to.Ptr("PowerState/Running"),
										},
									},
								},
							},
						},
					}, nil, nil)
				})

				It("should return error from excluded VM processing", func() {
					excludedNicMap := map[string]struct{}{
						strings.ToLower("ExcludedVM1-nic"): {},
					}
					expectedError := cgerror.CreateInternalError()

					nicReconcilerExclusion.EXPECT().AssociateNicInterfaceWithLBBackendpool(
						gomock.Any(), gomock.Any(), gomock.Any(),
						excludedNicMap, outboundOnlyPools, outboundOnlyPoolsIPv6,
					).Return(expectedError)

					err := reconcilerExclusion.AssociateLBBackendpool(
						context.Background(), computeSubscriptionID, networkSubscriptionID, resourceGroupName,
						allBackendPools, allBackendPoolsIPv6, excludedAgentPoolNames,
					)
					Expect(err).To(Equal(expectedError))
				})
			})

			When("VM listing fails", func() {
				It("should return the VM listing error", func() {
					expectedError := cgerror.CreateInternalError()
					vmClientExclusion.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return(nil, nil, expectedError)

					err := reconcilerExclusion.AssociateLBBackendpool(
						context.Background(), computeSubscriptionID, networkSubscriptionID, resourceGroupName,
						allBackendPools, allBackendPoolsIPv6, excludedAgentPoolNames,
					)
					Expect(err).To(Equal(expectedError))
				})
			})
		})

		Context("listAllOfVMInterfacesWithExclusionCategories", func() {
			var (
				mockCtrlCategories     *gomock.Controller
				vmClientCategories     *mock_vmclient.MockInterface
				reconcilerCategories   *VMAgentpoolBackendpoolReconciler
				excludedAgentPoolNames map[string]struct{}
				normalVM               compute.VirtualMachine
				excludedVM             compute.VirtualMachine
				vmWithoutPoolTag       compute.VirtualMachine
			)

			BeforeEach(func() {
				// Create separate mocks for categories tests
				mockCtrlCategories = gomock.NewController(GinkgoT())
				vmClientCategories = mock_vmclient.NewMockInterface(mockCtrlCategories)
				reconcilerCategories = &VMAgentpoolBackendpoolReconciler{
					vmClient:      vmClientCategories,
					nicReconciler: nil, // Not needed for these tests
				}

				excludedAgentPoolNames = map[string]struct{}{
					"excludedPool": {},
				}

				normalVM = compute.VirtualMachine{
					ID:   to.Ptr("NormalVM"),
					Name: to.Ptr("NormalVM"),
					Tags: map[string]*string{
						"poolName": to.Ptr("normalPool"),
					},
					VirtualMachineProperties: &compute.VirtualMachineProperties{
						NetworkProfile: &compute.NetworkProfile{
							NetworkInterfaces: &[]compute.NetworkInterfaceReference{
								{
									ID: to.Ptr("NormalVM-nic"),
								},
							},
						},
						InstanceView: &compute.VirtualMachineInstanceView{
							Statuses: &[]compute.InstanceViewStatus{
								{
									Code: to.Ptr("PowerState/Running"),
								},
							},
						},
					},
				}

				excludedVM = compute.VirtualMachine{
					ID:   to.Ptr("ExcludedVM"),
					Name: to.Ptr("ExcludedVM"),
					Tags: map[string]*string{
						"poolName": to.Ptr("excludedPool"),
					},
					VirtualMachineProperties: &compute.VirtualMachineProperties{
						NetworkProfile: &compute.NetworkProfile{
							NetworkInterfaces: &[]compute.NetworkInterfaceReference{
								{
									ID: to.Ptr("ExcludedVM-nic"),
								},
							},
						},
						InstanceView: &compute.VirtualMachineInstanceView{
							Statuses: &[]compute.InstanceViewStatus{
								{
									Code: to.Ptr("PowerState/Running"),
								},
							},
						},
					},
				}

				vmWithoutPoolTag = compute.VirtualMachine{
					ID:   to.Ptr("VMWithoutPoolTag"),
					Name: to.Ptr("VMWithoutPoolTag"),
					VirtualMachineProperties: &compute.VirtualMachineProperties{
						NetworkProfile: &compute.NetworkProfile{
							NetworkInterfaces: &[]compute.NetworkInterfaceReference{
								{
									ID: to.Ptr("VMWithoutPoolTag-nic"),
								},
							},
						},
						InstanceView: &compute.VirtualMachineInstanceView{
							Statuses: &[]compute.InstanceViewStatus{
								{
									Code: to.Ptr("PowerState/Running"),
								},
							},
						},
					},
				}
			})

			AfterEach(func() {
				mockCtrlCategories.Finish()
			})

			When("VMs have mixed normal and excluded agent pools", func() {
				It("should correctly categorize VMs into normal and excluded lists", func() {
					vmClientCategories.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]compute.VirtualMachine{
						normalVM,
						excludedVM,
					}, nil, nil)

					normalNicMap, excludedNicMap, err := reconcilerCategories.listAllOfVMInterfacesWithExclusionCategories(
						context.Background(), computeSubscriptionID, resourceGroupName, excludedAgentPoolNames,
					)

					Expect(err).To(BeNil())
					Expect(normalNicMap).To(Equal(map[string]struct{}{
						strings.ToLower("NormalVM-nic"): {},
					}))
					Expect(excludedNicMap).To(Equal(map[string]struct{}{
						strings.ToLower("ExcludedVM-nic"): {},
					}))
				})
			})

			When("all VMs are from normal agent pools", func() {
				It("should put all VMs in normal list and excluded list should be empty", func() {
					vmClientCategories.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]compute.VirtualMachine{
						normalVM,
					}, nil, nil)

					normalNicMap, excludedNicMap, err := reconcilerCategories.listAllOfVMInterfacesWithExclusionCategories(
						context.Background(), computeSubscriptionID, resourceGroupName, excludedAgentPoolNames,
					)

					Expect(err).To(BeNil())
					Expect(normalNicMap).To(Equal(map[string]struct{}{
						strings.ToLower("NormalVM-nic"): {},
					}))
					Expect(excludedNicMap).To(BeNil())
				})
			})

			When("all VMs are from excluded agent pools", func() {
				It("should put all VMs in excluded list and normal list should be empty", func() {
					vmClientCategories.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]compute.VirtualMachine{
						excludedVM,
					}, nil, nil)

					normalNicMap, excludedNicMap, err := reconcilerCategories.listAllOfVMInterfacesWithExclusionCategories(
						context.Background(), computeSubscriptionID, resourceGroupName, excludedAgentPoolNames,
					)

					Expect(err).To(BeNil())
					Expect(normalNicMap).To(BeNil())
					Expect(excludedNicMap).To(Equal(map[string]struct{}{
						strings.ToLower("ExcludedVM-nic"): {},
					}))
				})
			})

			When("excludedAgentPoolNames is nil", func() {
				It("should put all VMs in normal list", func() {
					vmClientCategories.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]compute.VirtualMachine{
						normalVM,
						excludedVM,
					}, nil, nil)

					normalNicMap, excludedNicMap, err := reconcilerCategories.listAllOfVMInterfacesWithExclusionCategories(
						context.Background(), computeSubscriptionID, resourceGroupName, nil,
					)

					Expect(err).To(BeNil())
					Expect(normalNicMap).To(Equal(map[string]struct{}{
						strings.ToLower("NormalVM-nic"):   {},
						strings.ToLower("ExcludedVM-nic"): {},
					}))
					Expect(excludedNicMap).To(BeNil())
				})
			})

			When("VM has no pool tag", func() {
				It("should skip VMs without pool tags", func() {
					vmClientCategories.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return([]compute.VirtualMachine{
						normalVM,
						vmWithoutPoolTag,
						excludedVM,
					}, nil, nil)

					normalNicMap, excludedNicMap, err := reconcilerCategories.listAllOfVMInterfacesWithExclusionCategories(
						context.Background(), computeSubscriptionID, resourceGroupName, excludedAgentPoolNames,
					)

					Expect(err).To(BeNil())
					Expect(normalNicMap).To(Equal(map[string]struct{}{
						strings.ToLower("NormalVM-nic"): {},
					}))
					Expect(excludedNicMap).To(Equal(map[string]struct{}{
						strings.ToLower("ExcludedVM-nic"): {},
					}))
					// VM without pool tag should not appear in either list
				})
			})

			When("VM client returns error", func() {
				It("should return the error", func() {
					expectedError := cgerror.CreateInternalError()
					vmClientCategories.EXPECT().ListVirtualMachinesWithInstanceView(gomock.Any(), gomock.Any(), gomock.Any(), true).Return(nil, nil, expectedError)

					normalNicMap, excludedNicMap, err := reconcilerCategories.listAllOfVMInterfacesWithExclusionCategories(
						context.Background(), computeSubscriptionID, resourceGroupName, excludedAgentPoolNames,
					)

					Expect(err).To(Equal(expectedError))
					Expect(normalNicMap).To(BeNil())
					Expect(excludedNicMap).To(BeNil())
				})
			})
		})
	})
})
