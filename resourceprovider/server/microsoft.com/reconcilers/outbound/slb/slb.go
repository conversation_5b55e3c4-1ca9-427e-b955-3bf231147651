package slb

import (
	"context"
	"fmt"
	"strings"

	"github.com/Azure/azure-sdk-for-go/services/network/mgmt/2022-07-01/network"
	"github.com/Azure/go-autorest/autorest/to"
	"k8s.io/utils/pointer"

	hcpEnums "go.goms.io/aks/rp/protos/hcp/types/enums/v1"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/asyncoperationsprocessor/rawgoalretriever"
	rpcommonconsts "go.goms.io/aks/rp/resourceprovider/server/microsoft.com/common/consts"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/goalresolvers"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/i18n"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/agentpool"
	"go.goms.io/aks/rp/resourceprovider/server/microsoft.com/reconcilers/outbound/utils"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/azureresources"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/azureresources/adapters"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/consts"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/flags"
	"go.goms.io/aks/rp/resourceprovider/sharedlib/tags"
	"go.goms.io/aks/rp/toolkit/apierror"
	"go.goms.io/aks/rp/toolkit/armerror"
	"go.goms.io/aks/rp/toolkit/azureclients/armclient"
	"go.goms.io/aks/rp/toolkit/azureclients/loadbalancer"
	"go.goms.io/aks/rp/toolkit/azureclients/publicipaddress"
	cgerror "go.goms.io/aks/rp/toolkit/categorizederror"
	"go.goms.io/aks/rp/toolkit/log"
	"go.goms.io/aks/rp/toolkit/logcore"
)

func New(
	loadbalancerClient loadbalancer.Interface,
	vmssReconciler agentpool.AgentPoolLBBackendpoolReconciler,
	vmReconciler agentpool.AgentPoolLBBackendpoolReconciler,
	vmReconcilerTrack2 agentpool.AgentPoolLBBackendpoolReconciler,
	pipClient publicipaddress.Interface,
	pipClientTrack2 azureresources.PublicIPAddressInterface,
	agentPoolsRetriever rawgoalretriever.AgentPoolsInterface,
	opt SlbOutBoundReconcilerFlag,
) outbound.Outbound {
	return &slbOutBoundReconciler{
		vmssReconciler:       vmssReconciler,
		vmReconciler:         vmReconciler,
		vmReconcilerTrack2:   vmReconcilerTrack2,
		loadbalancerClient:   loadbalancerClient,
		publicIPClient:       pipClient,
		publicIPClientTrack2: pipClientTrack2,
		agentPoolsRetriever:  agentPoolsRetriever,
		opt:                  opt,
	}
}

type SlbOutBoundReconcilerFlag interface {
	EnableTrack2SLBOutboundReconciler(context.Context, logcore.LogCore) bool
	EnableSDKTrack2PublicIPAddresses(context.Context, logcore.LogCore, ...flags.Track2FlagOption) bool
}

type slbOutBoundReconciler struct {
	vmssReconciler       agentpool.AgentPoolLBBackendpoolReconciler
	vmReconciler         agentpool.AgentPoolLBBackendpoolReconciler
	vmReconcilerTrack2   agentpool.AgentPoolLBBackendpoolReconciler
	loadbalancerClient   loadbalancer.Interface
	publicIPClient       publicipaddress.Interface
	publicIPClientTrack2 azureresources.PublicIPAddressInterface
	agentPoolsRetriever  rawgoalretriever.AgentPoolsInterface
	opt                  SlbOutBoundReconcilerFlag
}

// DeleteOutboundResources delete outbound backend pools and disassociate backendpools with vmss
func (c *slbOutBoundReconciler) DeleteOutboundResources(ctx context.Context, goal *goalresolvers.OutboundGoal) *cgerror.CategorizedError {
	ctx, span := log.StartSpan(ctx, "DeleteOutboundResources", log.AKSTeamNetworkIntegration)
	defer span.End()
	logger := log.MustGetLogger(ctx)

	var cerr *cgerror.CategorizedError
	existingLB, cerr := c.GetLoadBalancerAndValidateSKU(ctx, goal)
	if cerr != nil {
		return cerr
	}

	if existingLB == nil || existingLB.LoadBalancerPropertiesFormat == nil {
		return nil
	}
	var changed bool

	backendpoolIDs := make(map[string]struct{})
	var backendpoolIDsIPV6 map[string]struct{}

	backendpoolIDs[strings.ToLower(utils.GetLBBackEndPoolID(goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolName))] = struct{}{}
	if goal.EnableIPv6 {
		backendpoolIDsIPV6 = make(map[string]struct{})
		backendpoolIDsIPV6[strings.ToLower(utils.GetLBBackEndPoolID(goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolNameIPv6))] = struct{}{}
	}
	if (existingLB.LoadBalancingRules == nil || len(*existingLB.LoadBalancingRules) == 0) && (existingLB.InboundNatRules == nil || len(*existingLB.InboundNatRules) == 0) {
		if existingLB.BackendAddressPools != nil {
			for _, item := range *existingLB.BackendAddressPools {
				if item.Name != nil {
					backendpoolIDs[strings.ToLower(utils.GetLBBackEndPoolID(goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName, *item.Name))] = struct{}{}
					if goal.EnableIPv6 {
						backendpoolIDsIPV6[strings.ToLower(utils.GetLBBackEndPoolID(goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName, *item.Name))] = struct{}{}
					}
				}
			}
		}
	}
	if c.opt.EnableTrack2SLBOutboundReconciler(ctx, logger) {
		logger.Info(ctx, "DecoupleLBBackendPool: using track2 vmss reconciler")
		if err := c.vmReconcilerTrack2.DecoupleLBBackendPool(ctx, goal.ManagedClusterSubscriptionID, goal.NetworkSubscriptionID, goal.NodeResourceGroupName, backendpoolIDs, backendpoolIDsIPV6); err != nil {
			return err
		}
	} else {
		logger.Info(ctx, "DecoupleLBBackendPool: using track1 vmss reconciler")
		if err := c.vmReconciler.DecoupleLBBackendPool(ctx, goal.ManagedClusterSubscriptionID, goal.NetworkSubscriptionID, goal.NodeResourceGroupName, backendpoolIDs, backendpoolIDsIPV6); err != nil {
			return err
		}
	}
	if err := c.vmssReconciler.DecoupleLBBackendPool(ctx, goal.ManagedClusterSubscriptionID, goal.NetworkSubscriptionID, goal.NodeResourceGroupName, backendpoolIDs, backendpoolIDsIPV6); err != nil {
		return err
	}

	if deleteOutboundRules(ctx, goal, existingLB) {
		changed = true
	}
	if deleteOutboundBackendPools(ctx, existingLB, goal, backendpoolIDs) {
		changed = true
	}
	if changed {
		return c.ensureLoadbalancer(ctx, goal, existingLB, nil, nil)
	}
	return nil
}

// CleanUpFrontEndIPConfigs disassociate outbound ips and delete outbound rules if any
func (c *slbOutBoundReconciler) CleanUpFrontEndIPConfigs(ctx context.Context, goal *goalresolvers.OutboundGoal) *cgerror.CategorizedError {
	ctx, span := log.StartSpan(ctx, "CleanUpFrontEndIPConfigs", log.AKSTeamNetworkIntegration)
	defer span.End()
	existingLB, cerr := c.GetLoadBalancerAndValidateSKU(ctx, goal)
	if cerr != nil {
		return cerr
	}
	if existingLB == nil || existingLB.LoadBalancerPropertiesFormat == nil {
		return nil
	}

	var changed bool
	if deleteOutboundRules(ctx, goal, existingLB) {
		changed = true
	}

	if cleanupOutboundIPFrontendConfig(ctx, goal, existingLB, nil, nil) {
		changed = true
	}
	if changed {
		return c.ensureLoadbalancer(ctx, goal, existingLB, nil, nil)
	}
	return nil
}

func (c *slbOutBoundReconciler) AssociateFrontEndIPConfigs(ctx context.Context, goal *goalresolvers.OutboundGoal, outboundIPs map[string]adapters.PublicIPAddress, outboundIPPrefixes map[string]adapters.PublicIPPrefix) *cgerror.CategorizedError {
	ctx, span := log.StartSpan(ctx, "AssociateFrontEndIPConfigs", log.AKSTeamNetworkIntegration)
	defer span.End()

	existingLB, cerr := c.GetLoadBalancerAndValidateSKU(ctx, goal)
	if cerr != nil {
		return cerr
	}

	if existingLB == nil {
		cerr := cgerror.ToCategorizedError(fmt.Errorf("failed to get slb, please create slb first"))
		log.GetLogger(ctx).Errorf(ctx, "AssociateFrontEndIPConfigs: Error in put LB %q in resource group %q subscription id %s: '%s'",
			consts.SLBName,
			goal.NodeResourceGroupName,
			goal.NetworkSubscriptionID,
			cerr.Error())
		return cerr
	}

	return c.ensureLoadbalancer(ctx, goal, existingLB, outboundIPs, outboundIPPrefixes)
}

func (c *slbOutBoundReconciler) GetLoadBalancerAndValidateSKU(ctx context.Context, goal *goalresolvers.OutboundGoal) (*network.LoadBalancer, *cgerror.CategorizedError) {
	logger := log.GetLogger(ctx)
	slb, cerr := c.loadbalancerClient.GetLoadBalancer(ctx,
		goal.NetworkSubscriptionID,
		goal.NodeResourceGroupName,
		consts.SLBName)
	if cerr != nil {
		if cerr.IsNotFoundError() {
			logger.Infof(ctx, "GetLoadBalancerAndValidateSKU: LB %q does not exist in resource group %q subscription id %s. error: %s ", consts.SLBName, goal.NodeResourceGroupName, goal.NetworkSubscriptionID, cerr.Error())
			return nil, nil
		}
		if strings.Contains(cerr.Error(), string(armerror.NRPHostNameResolutionFailedMessage)) ||
			strings.Contains(cerr.Error(), string(apierror.NRPInternalServerError)) {
			cerr.OriginError = fmt.Errorf(cerr.OriginError.Error()+"%w", armerror.LoadBalancerError)
		}

		logger.Errorf(ctx, "GetLoadBalancerAndValidateSKU: GetLoadBalancer %q in resource group %q subscription id %s failed: '%s'",
			consts.SLBName,
			goal.NodeResourceGroupName,
			goal.NetworkSubscriptionID,
			cerr)

		return nil, cerr
	}

	if slb == nil || slb.ID == nil {
		return nil, nil
	}

	if slb.Sku == nil || slb.Sku.Name == "" {
		logger.Errorf(ctx, "validateSLBSKU: Existing LB %s does not have a SKU. Reconciliation cannot proceed.", *slb.ID)
		return nil, &cgerror.CategorizedError{
			Category:     apierror.InternalError,
			Code:         hcpEnums.ErrorCode_InvalidLoadBalancerSku,
			SubCode:      cgerror.UnknownLoadBalancerSku,
			Dependency:   cgerror.LB,
			Message:      i18n.GetTranslator(ctx).T(i18n.InternalServerError),
			InnerMessage: fmt.Sprintf("validateSLBSKU: Existing LB %s does not have a SKU", *slb.ID),
		}
	}

	if !strings.EqualFold(string(slb.Sku.Name), string(network.LoadBalancerSkuNameStandard)) {
		logger.Errorf(ctx, "validateSLBSKU: Existing LB %s is not a standard load balancer(current SKU: %s). Reconciliation cannot proceed.", *slb.ID, slb.Sku.Name)
		return nil, &cgerror.CategorizedError{
			Category:     apierror.InternalError,
			Code:         hcpEnums.ErrorCode_InvalidLoadBalancerSku,
			SubCode:      cgerror.NotStandardLoadBalancer,
			Dependency:   cgerror.LB,
			Message:      i18n.GetTranslator(ctx).T(i18n.InternalServerError),
			InnerMessage: fmt.Sprintf("validateSLBSKU: Existing LB %s is not a standard load balancer (current SKU: %s)", *slb.ID, slb.Sku.Name),
			Retriable:    to.BoolPtr(false),
		}
	}

	if !strings.EqualFold(to.String(slb.Location), goal.Location) {
		return nil, &cgerror.CategorizedError{
			Category:     apierror.InternalError,
			Code:         hcpEnums.ErrorCode_UnexpectedStateLoadBalancer,
			SubCode:      cgerror.UnexpectedStateLoadBalancer,
			Dependency:   cgerror.LB,
			Message:      i18n.GetTranslator(ctx).T(i18n.InternalServerErrorWithMessage, "unexpected Load Balancer state"),
			InnerMessage: fmt.Sprintf("existing loadbalancer %q location %q is different to expected location %q", consts.SLBName, to.String(slb.Location), goal.Location),
		}
	}

	if slb.ProvisioningState != network.ProvisioningStateSucceeded && slb.ProvisioningState != network.ProvisioningStateFailed {
		logger.Infof(ctx, "isTerminatingState - provisioningState: %s", string(slb.ProvisioningState))
		return nil, &cgerror.CategorizedError{
			Category:     apierror.InternalError,
			Code:         hcpEnums.ErrorCode_LBNotInTerminatingState,
			SubCode:      cgerror.LBNotInTerminatingState,
			Dependency:   cgerror.LB,
			Message:      i18n.GetTranslator(ctx).T(i18n.InternalServerErrorWithMessage, "Load Balancer not in terminating state"),
			InnerMessage: fmt.Sprintf("Existing LB %s is not in terminating state, current state %s", *slb.ID, slb.ProvisioningState),
		}
	}

	return slb, nil
}

func isIPBasedBackendPool(ctx context.Context, slb *network.LoadBalancer, backendPoolName string) bool {
	l := log.MustGetLogger(ctx)
	if slb != nil && slb.LoadBalancerPropertiesFormat != nil && slb.BackendAddressPools != nil {
		for _, backendAddressPool := range *slb.BackendAddressPools {
			if strings.EqualFold(pointer.StringDeref(backendAddressPool.Name, ""), backendPoolName) &&
				backendAddressPool.BackendAddressPoolPropertiesFormat != nil &&
				backendAddressPool.LoadBalancerBackendAddresses != nil {
				for _, loadBalancerBackendAddress := range *backendAddressPool.LoadBalancerBackendAddresses {
					if loadBalancerBackendAddress.LoadBalancerBackendAddressPropertiesFormat != nil {
						ip := pointer.StringDeref(loadBalancerBackendAddress.IPAddress, "")
						if ip != "" {
							l.Infof(ctx, "found IP address %s in backend pool %s, this is an IP-based backend pool", ip, pointer.StringDeref(backendAddressPool.Name, ""))
							return true
						}
					}
				}
			}
		}
	}
	return false
}

// getExcludedAgentPoolNames retrieves agent pools that have the exclude label set to "true"
func (c *slbOutBoundReconciler) getExcludedAgentPoolNames(ctx context.Context) (map[string]struct{}, *cgerror.CategorizedError) {
	agentPools, cerr := c.agentPoolsRetriever.ListAgentPools(ctx, false)
	if cerr != nil {
		return nil, cerr
	}

	excludedPools := make(map[string]struct{})
	for _, agentPool := range agentPools {
		if agentPool.Properties != nil && agentPool.Properties.CustomNodeLabels != nil {
			if value, ok := agentPool.Properties.CustomNodeLabels[rpcommonconsts.NodeLabelExcludeFromLoadBalancerInbound]; ok && strings.EqualFold(value, "true") {
				log.GetLogger(ctx).Infof(ctx, "excluding agent pool %s from inbound backend pool due to label %s=%s",
					agentPool.Properties.Name, rpcommonconsts.NodeLabelExcludeFromLoadBalancerInbound, value)
				excludedPools[agentPool.Properties.Name] = struct{}{}
			}
		}
	}
	return excludedPools, nil
}

// EnsureOutboundResources ensure outbound backend pools and associate backendpools with vmss
func (c *slbOutBoundReconciler) EnsureOutboundResources(ctx context.Context, goal *goalresolvers.OutboundGoal, outboundIPs map[string]adapters.PublicIPAddress, outboundIPPrefixes map[string]adapters.PublicIPPrefix) *cgerror.CategorizedError {
	ctx, span := log.StartSpan(ctx, "EnsureOutboundResources", log.AKSTeamNetworkIntegration)
	defer span.End()
	logger := log.MustGetLogger(ctx)

	var cerr *cgerror.CategorizedError
	existingLB, cerr := c.GetLoadBalancerAndValidateSKU(ctx, goal)
	if cerr != nil {
		return cerr
	}
	var changed bool
	if existingLB == nil {
		existingLB = &network.LoadBalancer{
			Name:     to.StringPtr(consts.SLBName),
			Location: &goal.Location,
			Sku: &network.LoadBalancerSku{
				Name: network.LoadBalancerSkuNameStandard,
			},
			LoadBalancerPropertiesFormat: &network.LoadBalancerPropertiesFormat{
				FrontendIPConfigurations: &[]network.FrontendIPConfiguration{},
			},
		}
		if goal.ExtendedLocation != nil {
			existingLB.ExtendedLocation = &network.ExtendedLocation{
				Name: to.StringPtr(goal.ExtendedLocation.Name),
				Type: network.ExtendedLocationTypes(goal.ExtendedLocation.Type),
			}
		}
		changed = true
	}
	if ensureOutboundBackendPools(ctx, existingLB, goal) {
		changed = true
	}
	if changed {
		// ensure backendpool
		if err := c.ensureLoadbalancer(ctx, goal, existingLB, outboundIPs, outboundIPPrefixes); err != nil {
			return err
		}
	}

	// Get excluded agent pool names
	excludedAgentPoolNames, cerr := c.getExcludedAgentPoolNames(ctx)
	if cerr != nil {
		return cerr
	}

	backendpoolIDs := make(map[string]struct{})
	backendpoolIDsIPV6 := make(map[string]struct{})

	backendpoolIDs[strings.ToLower(utils.GetLBBackEndPoolID(goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolName))] = struct{}{}
	if goal.ManagedSLBGoal.BackendPoolType == rpcommonconsts.LBBackendPoolTypeNodeIPConfiguration &&
		!isIPBasedBackendPool(ctx, existingLB, consts.SlbInboundBackendPoolName) {
		backendpoolIDs[strings.ToLower(utils.GetLBBackEndPoolID(goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName, consts.SlbInboundBackendPoolName))] = struct{}{}
	}
	if goal.EnableIPv6 {
		backendpoolIDsIPV6[strings.ToLower(utils.GetLBBackEndPoolID(goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName, consts.SlbOutboundBackendPoolNameIPv6))] = struct{}{}
		if goal.ManagedSLBGoal.BackendPoolType == rpcommonconsts.LBBackendPoolTypeNodeIPConfiguration &&
			!isIPBasedBackendPool(ctx, existingLB, consts.SlbInboundBackendPoolNameIPv6) {
			backendpoolIDsIPV6[strings.ToLower(utils.GetLBBackEndPoolID(goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName, consts.SlbInboundBackendPoolNameIPv6))] = struct{}{}
		}
	}
	if err := c.vmssReconciler.AssociateLBBackendpool(ctx, goal.ManagedClusterSubscriptionID, goal.NetworkSubscriptionID, goal.NodeResourceGroupName, backendpoolIDs, backendpoolIDsIPV6, excludedAgentPoolNames); err != nil {
		return err
	}
	if c.opt.EnableTrack2SLBOutboundReconciler(ctx, logger) {
		logger.Info(ctx, "AssociateLBBackendpool: using track2 vmss reconciler")
		if err := c.vmReconcilerTrack2.AssociateLBBackendpool(ctx, goal.ManagedClusterSubscriptionID, goal.NetworkSubscriptionID, goal.NodeResourceGroupName, backendpoolIDs, backendpoolIDsIPV6, excludedAgentPoolNames); err != nil {
			return err
		}
	} else {
		logger.Info(ctx, "AssociateLBBackendpool: using track1 vmss reconciler")
		if err := c.vmReconciler.AssociateLBBackendpool(ctx, goal.ManagedClusterSubscriptionID, goal.NetworkSubscriptionID, goal.NodeResourceGroupName, backendpoolIDs, backendpoolIDsIPV6, excludedAgentPoolNames); err != nil {
			return err
		}
	}
	return nil
}

// ensureLoadbalancer reconcile slb to expected state,
// if no lb rules ,outbound rules and inbound nat rules are defined, slb will be deleted
// if frontend ip configs are not referred, fipconfig which is not referred will be deleted
// if no frontend ip configs are defined, we will create a temporary frontend ip config
func (c *slbOutBoundReconciler) ensureLoadbalancer(ctx context.Context, goal *goalresolvers.OutboundGoal, existingLB *network.LoadBalancer, outboundIPs map[string]adapters.PublicIPAddress, outboundIPPrefixes map[string]adapters.PublicIPPrefix) *cgerror.CategorizedError {
	ctx, span := log.StartSpan(ctx, "ensureLoadbalancer", log.AKSTeamNetworkIntegration)
	defer span.End()
	log.GetLogger(ctx).Infof(ctx, "ensureLoadbalancer: Begin to put LB %q in resource group %q subscription id %s",
		consts.SLBName,
		goal.NodeResourceGroupName,
		goal.NetworkSubscriptionID)

	if (existingLB.LoadBalancingRules == nil || len(*existingLB.LoadBalancingRules) == 0) && (existingLB.OutboundRules == nil || len(*existingLB.OutboundRules) == 0) && (existingLB.InboundNatRules == nil || len(*existingLB.InboundNatRules) == 0) && (*existingLB.BackendAddressPools == nil || len(*existingLB.BackendAddressPools) == 0) {
		//all of configs are removed. we can't create a slb without any fip config. so we delete slb
		if cerr := c.loadbalancerClient.DeleteLoadBalancer(ctx, goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName); cerr != nil {
			log.GetLogger(ctx).Warningf(ctx, "ensureLoadBalancer:  error in delete LBs in resource group %q subscription id %s.",
				goal.NodeResourceGroupName,
				goal.NetworkSubscriptionID)
			return cerr
		}
		return c.deleteTempOutboundPIPFromGoal(ctx, goal)
	}
	if len(outboundIPs) != 0 || len(outboundIPPrefixes) != 0 {
		log.GetLogger(ctx).Infof(ctx, "ensureLoadbalancer: Update outbound ip frontend config for %q in resource group %q subscription id %s",
			consts.SLBName,
			goal.NodeResourceGroupName,
			goal.NetworkSubscriptionID)
		newIPv4IPConfigs, newIPv6IPConfigs, _ := ensureOutboundIPFrontendConfig(ctx, goal, existingLB, outboundIPs, outboundIPPrefixes)
		ensureOutboundRules(ctx, goal, existingLB, newIPv4IPConfigs, newIPv6IPConfigs)
	}

	cleanupOutboundIPFrontendConfig(ctx, goal, existingLB, outboundIPs, outboundIPPrefixes)
	var tempPipDeleted bool
	if existingLB.FrontendIPConfigurations == nil || len(*existingLB.FrontendIPConfigurations) == 0 {
		//create temporary pip
		log.GetLogger(ctx).Infof(ctx, "ensureLoadbalancer: Create temporary PIP for %q in resource group %q subscription id %s",
			consts.SLBName,
			goal.NodeResourceGroupName,
			goal.NetworkSubscriptionID)
		var tempPIPID *string
		var cerr *cgerror.CategorizedError
		if c.opt.EnableSDKTrack2PublicIPAddresses(ctx, log.GetLogger(ctx)) {
			tempPIPID, cerr = c.ensureTempOutboundPIPFromGoalByTrack2(ctx, goal)
		} else {
			tempPIPID, cerr = c.ensureTempOutboundPIPFromGoal(ctx, goal)
		}
		if cerr != nil {
			return cerr
		}
		existingLB.FrontendIPConfigurations = &[]network.FrontendIPConfiguration{
			{
				Name: to.StringPtr(SLBTemporaryPIPName),
				FrontendIPConfigurationPropertiesFormat: &network.FrontendIPConfigurationPropertiesFormat{
					PublicIPAddress: &network.PublicIPAddress{
						ID: tempPIPID,
					},
				},
			},
		}
	} else {
		tempPipDeleted = true
	}

	// merge existing and goal lb tags, and overwrite the existing lb tags with goal lb ones when there're conflicts.
	var cerr *cgerror.CategorizedError
	existingLB.Tags, cerr = tags.MergeTags(ctx,
		armclient.GetResourceID(goal.NetworkSubscriptionID, goal.NodeResourceGroupName, "Microsoft.Network/loadBalancers", consts.SLBName),
		"Microsoft.Network/loadBalancers",
		existingLB.Tags,
		goal.ExtraResourceTags,
		map[string]*string{
			tags.ClusterName: to.StringPtr(goal.ManagedClusterName),
			tags.ClusterRG:   to.StringPtr(goal.ManagedClusterResourceGroupName),
		},
	)
	if cerr != nil {
		return cerr
	}

	result, cerr := c.loadbalancerClient.CreateOrUpdateLoadBalancer(ctx, goal.NetworkSubscriptionID, goal.NodeResourceGroupName, consts.SLBName, existingLB)
	if cerr != nil {
		log.GetLogger(ctx).Errorf(ctx, "ensureLoadbalancer: Error in put LB %q in resource group %q subscription id %s: %s",
			consts.SLBName,
			goal.NodeResourceGroupName,
			goal.NetworkSubscriptionID,
			cerr.Error())

		if strings.Contains(cerr.Error(), string(armerror.NRPHostNameResolutionFailedMessage)) ||
			strings.Contains(cerr.Error(), string(apierror.NRPInternalServerError)) {
			cerr.OriginError = fmt.Errorf(cerr.OriginError.Error()+"%w", armerror.LoadBalancerError)
		}
		return cerr.SetCode(hcpEnums.ErrorCode_CreateOrUpdateLoadBalancerError)
	}

	log.GetLogger(ctx).Infof(ctx, "ensureLoadbalancer: Succeeded in put LB %q in resource group %q subscription id %s, result: %s",
		consts.SLBName,
		goal.NodeResourceGroupName,
		goal.NetworkSubscriptionID,
		to.String(result.Name))

	if tempPipDeleted {
		log.GetLogger(ctx).Infof(ctx, "ensureLoadbalancer: Delete temporary PIP for %q in resource group %q subscription id %s",
			consts.SLBName,
			goal.NodeResourceGroupName,
			goal.NetworkSubscriptionID)
		return c.deleteTempOutboundPIPFromGoal(ctx, goal)
	}

	return nil
}
